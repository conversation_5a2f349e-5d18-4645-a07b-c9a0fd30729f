#!/usr/bin/env python3
"""
Test script for the category selection APIs
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db_config.pyqs_admin_db import (
    get_levels_by_site_id,
    get_syllabus_by_level,
    get_subjects_by_syllabus,
    get_grades_wonderpublish_style
)

def test_levels():
    """Test getting levels"""
    print("Testing get_levels_by_site_id...")
    levels = get_levels_by_site_id()
    print(f"Found {len(levels)} levels:")
    for level in levels[:5]:  # Show first 5
        print(f"  - {level}")
    return levels

def test_syllabi(level_name):
    """Test getting syllabi for a level"""
    print(f"\nTesting get_syllabus_by_level for level: {level_name}")
    syllabi = get_syllabus_by_level(level_name)
    print(f"Found {len(syllabi)} syllabi:")
    for syllabus in syllabi[:5]:  # Show first 5
        print(f"  - {syllabus}")
    return syllabi

def test_subjects(syllabus_name):
    """Test getting subjects for a syllabus"""
    print(f"\nTesting get_subjects_by_syllabus for syllabus: {syllabus_name}")
    subjects = get_subjects_by_syllabus(syllabus_name)
    print(f"Found {len(subjects)} subjects:")
    for subject in subjects[:5]:  # Show first 5
        print(f"  - {subject}")
    return subjects

def test_grades(syllabus_name):
    """Test getting grades for a syllabus"""
    print(f"\nTesting get_grades_wonderpublish_style for syllabus: {syllabus_name}")
    grades = get_grades_wonderpublish_style(syllabus_name)
    print(f"Found {len(grades)} grades:")
    for grade in grades[:5]:  # Show first 5
        print(f"  - {grade}")
    return grades

def main():
    """Main test function"""
    try:
        # Test levels
        levels = test_levels()
        
        if levels:
            # Test syllabi with first level
            level_name = levels[0]['name']
            syllabi = test_syllabi(level_name)
            
            if syllabi:
                # Test subjects and grades with first syllabus
                syllabus_name = syllabi[0]['syllabus']
                test_subjects(syllabus_name)
                test_grades(syllabus_name)
            else:
                print("No syllabi found to test subjects and grades")
        else:
            print("No levels found to test further")
            
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
